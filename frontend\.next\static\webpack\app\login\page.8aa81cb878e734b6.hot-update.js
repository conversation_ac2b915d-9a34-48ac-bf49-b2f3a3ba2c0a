"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/login/page",{

/***/ "(app-pages-browser)/./src/hooks/useAuth.ts":
/*!******************************!*\
  !*** ./src/hooks/useAuth.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth auto */ \n\nconst useAuth = ()=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAuth.useEffect\": ()=>{\n            // Obter sessão inicial\n            const getInitialSession = {\n                \"useAuth.useEffect.getInitialSession\": async ()=>{\n                    const { data: { session }, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.getSession();\n                    if (error) {\n                        setError({\n                            message: error.message\n                        });\n                    } else {\n                        setSession(session);\n                        var _session_user;\n                        setUser((_session_user = session === null || session === void 0 ? void 0 : session.user) !== null && _session_user !== void 0 ? _session_user : null);\n                    }\n                    setIsLoading(false);\n                }\n            }[\"useAuth.useEffect.getInitialSession\"];\n            getInitialSession();\n            // Escutar mudanças de autenticação\n            const { data: { subscription } } = _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.onAuthStateChange({\n                \"useAuth.useEffect\": async (event, session)=>{\n                    setSession(session);\n                    var _session_user;\n                    setUser((_session_user = session === null || session === void 0 ? void 0 : session.user) !== null && _session_user !== void 0 ? _session_user : null);\n                    setIsLoading(false);\n                    if (event === 'SIGNED_OUT') {\n                        setError(null);\n                    }\n                }\n            }[\"useAuth.useEffect\"]);\n            return ({\n                \"useAuth.useEffect\": ()=>subscription.unsubscribe()\n            })[\"useAuth.useEffect\"];\n        }\n    }[\"useAuth.useEffect\"], []);\n    // Função para login com email/senha\n    const signInWithEmail = async (email, password)=>{\n        setIsLoading(true);\n        setError(null);\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        if (error) {\n            setError({\n                message: error.message\n            });\n            setIsLoading(false);\n            return {\n                success: false,\n                error\n            };\n        }\n        setIsLoading(false);\n        return {\n            success: true,\n            data\n        };\n    };\n    // Função para registro com email/senha (direto)\n    const signUpWithEmail = async (email, password)=>{\n        setIsLoading(true);\n        setError(null);\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.signUp({\n            email,\n            password\n        });\n        if (error) {\n            setError({\n                message: error.message\n            });\n            setIsLoading(false);\n            return {\n                success: false,\n                error\n            };\n        }\n        setIsLoading(false);\n        return {\n            success: true,\n            data\n        };\n    };\n    // Função para finalizar signup após OTP (definir senha final)\n    const completeSignupWithPassword = async (email, password)=>{\n        setIsLoading(true);\n        setError(null);\n        // Fazer signup com a senha definida pelo usuário\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.signUp({\n            email,\n            password\n        });\n        if (error) {\n            setError({\n                message: error.message\n            });\n            setIsLoading(false);\n            return {\n                success: false,\n                error\n            };\n        }\n        setIsLoading(false);\n        return {\n            success: true,\n            data\n        };\n    };\n    // Função para login com Google\n    const signInWithGoogle = async ()=>{\n        setIsLoading(true);\n        setError(null);\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.signInWithOAuth({\n            provider: 'google',\n            options: {\n                redirectTo: \"\".concat(window.location.origin, \"/dashboard\")\n            }\n        });\n        if (error) {\n            setError({\n                message: error.message\n            });\n            setIsLoading(false);\n            return {\n                success: false,\n                error\n            };\n        }\n        return {\n            success: true,\n            data\n        };\n    };\n    // Função para enviar OTP por email (para signup)\n    const signUpWithOTP = async (email)=>{\n        setIsLoading(true);\n        setError(null);\n        // Gerar um código OTP aleatório\n        const otpCode = Math.floor(100000 + Math.random() * 900000).toString();\n        // Armazenar o código temporariamente (em produção, use um backend)\n        localStorage.setItem(\"otp_\".concat(email), otpCode);\n        localStorage.setItem(\"otp_\".concat(email, \"_timestamp\"), Date.now().toString());\n        // Simular envio de email (em produção, use um serviço de email real)\n        console.log(\"\\uD83D\\uDCE7 C\\xf3digo OTP para \".concat(email, \": \").concat(otpCode));\n        // Mostrar o código na tela para teste (remover em produção)\n        alert(\"C\\xf3digo OTP para teste: \".concat(otpCode));\n        setIsLoading(false);\n        return {\n            success: true,\n            data: {\n                email\n            }\n        };\n    };\n    // Função para verificar OTP (para signup)\n    const verifySignupOTP = async (email, token)=>{\n        setIsLoading(true);\n        setError(null);\n        // Verificar o código armazenado localmente\n        const storedOtp = localStorage.getItem(\"otp_\".concat(email));\n        const timestamp = localStorage.getItem(\"otp_\".concat(email, \"_timestamp\"));\n        if (!storedOtp || !timestamp) {\n            setError({\n                message: 'Código OTP não encontrado. Solicite um novo código.'\n            });\n            setIsLoading(false);\n            return {\n                success: false,\n                error: {\n                    message: 'OTP não encontrado'\n                }\n            };\n        }\n        // Verificar se o código não expirou (5 minutos)\n        const now = Date.now();\n        const otpTime = parseInt(timestamp);\n        const fiveMinutes = 5 * 60 * 1000;\n        if (now - otpTime > fiveMinutes) {\n            localStorage.removeItem(\"otp_\".concat(email));\n            localStorage.removeItem(\"otp_\".concat(email, \"_timestamp\"));\n            setError({\n                message: 'Código OTP expirado. Solicite um novo código.'\n            });\n            setIsLoading(false);\n            return {\n                success: false,\n                error: {\n                    message: 'OTP expirado'\n                }\n            };\n        }\n        // Verificar se o código está correto\n        if (token !== storedOtp) {\n            setError({\n                message: 'Código OTP inválido.'\n            });\n            setIsLoading(false);\n            return {\n                success: false,\n                error: {\n                    message: 'OTP inválido'\n                }\n            };\n        }\n        // Código válido - limpar do localStorage\n        localStorage.removeItem(\"otp_\".concat(email));\n        localStorage.removeItem(\"otp_\".concat(email, \"_timestamp\"));\n        setIsLoading(false);\n        return {\n            success: true,\n            data: {\n                email\n            }\n        };\n    };\n    // Função para logout\n    const signOut = async ()=>{\n        setIsLoading(true);\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.signOut();\n        if (error) {\n            setError({\n                message: error.message\n            });\n        }\n        setIsLoading(false);\n        return {\n            success: !error,\n            error\n        };\n    };\n    return {\n        user,\n        session,\n        isLoading,\n        error,\n        signInWithEmail,\n        signUpWithEmail,\n        completeSignupWithPassword,\n        signInWithGoogle,\n        signUpWithOTP,\n        verifySignupOTP,\n        signOut\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAuth.ts\n"));

/***/ })

});