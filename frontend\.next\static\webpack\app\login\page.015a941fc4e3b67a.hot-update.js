"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/login/page",{

/***/ "(app-pages-browser)/./src/hooks/useAuth.ts":
/*!******************************!*\
  !*** ./src/hooks/useAuth.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth auto */ \n\nconst useAuth = ()=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAuth.useEffect\": ()=>{\n            // Obter sessão inicial\n            const getInitialSession = {\n                \"useAuth.useEffect.getInitialSession\": async ()=>{\n                    const { data: { session }, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.getSession();\n                    if (error) {\n                        setError({\n                            message: error.message\n                        });\n                    } else {\n                        setSession(session);\n                        var _session_user;\n                        setUser((_session_user = session === null || session === void 0 ? void 0 : session.user) !== null && _session_user !== void 0 ? _session_user : null);\n                    }\n                    setIsLoading(false);\n                }\n            }[\"useAuth.useEffect.getInitialSession\"];\n            getInitialSession();\n            // Escutar mudanças de autenticação\n            const { data: { subscription } } = _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.onAuthStateChange({\n                \"useAuth.useEffect\": async (event, session)=>{\n                    setSession(session);\n                    var _session_user;\n                    setUser((_session_user = session === null || session === void 0 ? void 0 : session.user) !== null && _session_user !== void 0 ? _session_user : null);\n                    setIsLoading(false);\n                    if (event === 'SIGNED_OUT') {\n                        setError(null);\n                    }\n                }\n            }[\"useAuth.useEffect\"]);\n            return ({\n                \"useAuth.useEffect\": ()=>subscription.unsubscribe()\n            })[\"useAuth.useEffect\"];\n        }\n    }[\"useAuth.useEffect\"], []);\n    // Função para login com email/senha\n    const signInWithEmail = async (email, password)=>{\n        setIsLoading(true);\n        setError(null);\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        if (error) {\n            setError({\n                message: error.message\n            });\n            setIsLoading(false);\n            return {\n                success: false,\n                error\n            };\n        }\n        setIsLoading(false);\n        return {\n            success: true,\n            data\n        };\n    };\n    // Função para registro com email/senha (direto)\n    const signUpWithEmail = async (email, password)=>{\n        setIsLoading(true);\n        setError(null);\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.signUp({\n            email,\n            password\n        });\n        if (error) {\n            setError({\n                message: error.message\n            });\n            setIsLoading(false);\n            return {\n                success: false,\n                error\n            };\n        }\n        setIsLoading(false);\n        return {\n            success: true,\n            data\n        };\n    };\n    // Função para finalizar signup após OTP (definir senha final)\n    const completeSignupWithPassword = async (email, password)=>{\n        setIsLoading(true);\n        setError(null);\n        // Fazer signup com a senha definida pelo usuário\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.signUp({\n            email,\n            password\n        });\n        if (error) {\n            setError({\n                message: error.message\n            });\n            setIsLoading(false);\n            return {\n                success: false,\n                error\n            };\n        }\n        setIsLoading(false);\n        return {\n            success: true,\n            data\n        };\n    };\n    // Função para login com Google\n    const signInWithGoogle = async ()=>{\n        setIsLoading(true);\n        setError(null);\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.signInWithOAuth({\n            provider: 'google',\n            options: {\n                redirectTo: \"\".concat(window.location.origin, \"/dashboard\")\n            }\n        });\n        if (error) {\n            setError({\n                message: error.message\n            });\n            setIsLoading(false);\n            return {\n                success: false,\n                error\n            };\n        }\n        return {\n            success: true,\n            data\n        };\n    };\n    // Função para enviar OTP por email (para signup)\n    const signUpWithOTP = async (email)=>{\n        setIsLoading(true);\n        setError(null);\n        // Gerar um código OTP aleatório\n        const otpCode = Math.floor(100000 + Math.random() * 900000).toString();\n        // Armazenar o código temporariamente (em produção, use um backend)\n        localStorage.setItem(\"otp_\".concat(email), otpCode);\n        localStorage.setItem(\"otp_\".concat(email, \"_timestamp\"), Date.now().toString());\n        // Simular envio de email (em produção, use um serviço de email real)\n        console.log(\"\\uD83D\\uDCE7 C\\xf3digo OTP para \".concat(email, \": \").concat(otpCode));\n        // Mostrar o código na tela para teste (remover em produção)\n        alert(\"C\\xf3digo OTP para teste: \".concat(otpCode));\n        setIsLoading(false);\n        return {\n            success: true,\n            data: {\n                email\n            }\n        };\n    };\n    // Função para verificar OTP (para signup)\n    const verifySignupOTP = async (email, token)=>{\n        setIsLoading(true);\n        setError(null);\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.verifyOtp({\n            email,\n            token,\n            type: 'email'\n        });\n        if (error) {\n            setError({\n                message: error.message\n            });\n            setIsLoading(false);\n            return {\n                success: false,\n                error\n            };\n        }\n        // Fazer logout imediatamente para não autenticar o usuário ainda\n        await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.signOut();\n        setIsLoading(false);\n        return {\n            success: true,\n            data\n        };\n    };\n    // Função para logout\n    const signOut = async ()=>{\n        setIsLoading(true);\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.signOut();\n        if (error) {\n            setError({\n                message: error.message\n            });\n        }\n        setIsLoading(false);\n        return {\n            success: !error,\n            error\n        };\n    };\n    return {\n        user,\n        session,\n        isLoading,\n        error,\n        signInWithEmail,\n        signUpWithEmail,\n        completeSignupWithPassword,\n        signInWithGoogle,\n        signUpWithOTP,\n        verifySignupOTP,\n        signOut\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAuth.ts\n"));

/***/ })

});